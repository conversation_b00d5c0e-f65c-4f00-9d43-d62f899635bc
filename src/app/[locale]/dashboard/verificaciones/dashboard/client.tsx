'use client';

import { useState, Suspense } from 'react';
import { 
  Box, 
  Container, 
  Heading,
  VStack,
  Card,
  CardHeader,
  CardBody,
  SimpleGrid,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  StatArrow,
  useColorModeValue,
  Alert,
  AlertIcon,
  Button,
  HStack,
  Select,
  Text,
  Progress,
  Badge,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  Divider,
} from '@chakra-ui/react';
import { FiTrendingUp, FiTrendingDown, FiAlertTriangle, FiCheckCircle } from 'react-icons/fi';
import { useQuery } from '@tanstack/react-query';
import { getAdminDashboard, getAllVerifications, getVerificationCenters } from '@/actions/verification';

export function VerificationDashboardPage() {
  const [timeRange, setTimeRange] = useState('30');
  const bgColor = useColorModeValue('white', 'gray.800');

  // Fetch dashboard data
  const { data: dashboardData, isLoading: dashboardLoading } = useQuery({
    queryKey: ['admin-dashboard'],
    queryFn: getAdminDashboard,
    refetchInterval: 30000, // Refresh every 30 seconds
  });

  // Fetch recent verifications
  const { data: recentVerifications } = useQuery({
    queryKey: ['recent-verifications'],
    queryFn: () => getAllVerifications({ page: 1, limit: 10 }),
  });

  // Fetch verification centers
  const { data: centers } = useQuery({
    queryKey: ['verification-centers'],
    queryFn: () => getVerificationCenters(),
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'green';
      case 'pending_customer': return 'yellow';
      case 'expired': return 'red';
      default: return 'gray';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'completed': return 'Completada';
      case 'pending_customer': return 'Pendiente Cliente';
      case 'expired': return 'Expirada';
      default: return status;
    }
  };

  return (
    <Suspense fallback={
      <Container maxW="7xl" py={8}>
        <Alert status="info">
          <AlertIcon />
          Cargando dashboard de verificaciones...
        </Alert>
      </Container>
    }>
      <Container maxW="7xl" py={8}>
      <VStack spacing={6} align="stretch">
        {/* Header */}
        <Card bg={bgColor}>
          <CardHeader>
            <HStack justify="space-between">
              <Heading size="lg">Dashboard de Verificaciones</Heading>
              <Select maxW="200px" value={timeRange} onChange={(e) => setTimeRange(e.target.value)}>
                <option value="7">Últimos 7 días</option>
                <option value="30">Últimos 30 días</option>
                <option value="90">Últimos 90 días</option>
              </Select>
            </HStack>
          </CardHeader>
        </Card>

        {/* Main Stats */}
        {dashboardData && (
          <SimpleGrid columns={[1, 2, 4]} spacing={6}>
            <Card bg={bgColor}>
              <CardBody>
                <Stat>
                  <HStack>
                    <Box>
                      <StatLabel>Total Verificaciones</StatLabel>
                      <StatNumber>{dashboardData.summary.totalVerifications.toLocaleString()}</StatNumber>
                    </Box>
                    <FiCheckCircle size="24" color="green" />
                  </HStack>
                  <StatHelpText>
                    <StatArrow type="increase" />
                    Tasa de finalización: {dashboardData.summary.completionRate}%
                  </StatHelpText>
                </Stat>
              </CardBody>
            </Card>

            <Card bg={bgColor}>
              <CardBody>
                <Stat>
                  <HStack>
                    <Box>
                      <StatLabel>Pendientes</StatLabel>
                      <StatNumber color="yellow.500">{dashboardData.summary.pendingCustomer}</StatNumber>
                    </Box>
                    <FiAlertTriangle size="24" color="orange" />
                  </HStack>
                  <StatHelpText>Esperando evidencias del cliente</StatHelpText>
                </Stat>
              </CardBody>
            </Card>

            <Card bg={bgColor}>
              <CardBody>
                <Stat>
                  <HStack>
                    <Box>
                      <StatLabel>Completadas</StatLabel>
                      <StatNumber color="green.500">{dashboardData.summary.completedVerifications}</StatNumber>
                    </Box>
                    <FiTrendingUp size="24" color="green" />
                  </HStack>
                  <StatHelpText>
                    {Math.round((dashboardData.summary.completedVerifications / dashboardData.summary.totalVerifications) * 100)}% del total
                  </StatHelpText>
                </Stat>
              </CardBody>
            </Card>

            <Card bg={bgColor}>
              <CardBody>
                <Stat>
                  <HStack>
                    <Box>
                      <StatLabel>Este Mes</StatLabel>
                      <StatNumber>{dashboardData.recent.last30Days}</StatNumber>
                    </Box>
                    <FiTrendingUp size="24" color="blue" />
                  </HStack>
                  <StatHelpText>Nuevas verificaciones</StatHelpText>
                </Stat>
              </CardBody>
            </Card>
          </SimpleGrid>
        )}

        {/* Alerts Section */}
        {dashboardData?.alerts && (
          <SimpleGrid columns={[1, 2]} spacing={4}>
            <Alert status="warning">
              <AlertIcon />
              <VStack align="start" spacing={1}>
                <Box fontWeight="bold">Vencimientos Próximos</Box>
                <Box>{dashboardData.alerts.upcomingDue} verificaciones vencen en los próximos 30 días</Box>
              </VStack>
            </Alert>
            
            <Alert status="error">
              <AlertIcon />
              <VStack align="start" spacing={1}>
                <Box fontWeight="bold">Verificaciones Vencidas</Box>
                <Box>{dashboardData.alerts.overdue} verificaciones requieren atención inmediata</Box>
              </VStack>
            </Alert>
          </SimpleGrid>
        )}

        <SimpleGrid columns={[1, 2]} spacing={6}>
          {/* Progress Overview */}
          <Card bg={bgColor}>
            <CardHeader>
              <Heading size="md">Progreso de Verificaciones</Heading>
            </CardHeader>
            <CardBody>
              <VStack spacing={4}>
                {dashboardData && (
                  <>
                    <Box width="full">
                      <HStack justify="space-between" mb={2}>
                        <Text>Completadas</Text>
                        <Text fontWeight="bold">{dashboardData.summary.completionRate}%</Text>
                      </HStack>
                      <Progress 
                        value={dashboardData.summary.completionRate} 
                        colorScheme="green" 
                        size="lg" 
                        hasStripe 
                        isAnimated
                      />
                    </Box>
                    
                    <Divider />
                    
                    <SimpleGrid columns={2} spacing={4} width="full">
                      <Box textAlign="center">
                        <Text fontSize="2xl" fontWeight="bold" color="green.500">
                          {dashboardData.summary.completedVerifications}
                        </Text>
                        <Text fontSize="sm" color="gray.500">Completadas</Text>
                      </Box>
                      <Box textAlign="center">
                        <Text fontSize="2xl" fontWeight="bold" color="yellow.500">
                          {dashboardData.summary.pendingCustomer}
                        </Text>
                        <Text fontSize="sm" color="gray.500">Pendientes</Text>
                      </Box>
                    </SimpleGrid>
                  </>
                )}
              </VStack>
            </CardBody>
          </Card>

          {/* Centers Overview */}
          <Card bg={bgColor}>
            <CardHeader>
              <Heading size="md">Centros de Verificación</Heading>
            </CardHeader>
            <CardBody>
              <VStack spacing={4}>
                {dashboardData && (
                  <>
                    <SimpleGrid columns={2} spacing={4} width="full">
                      <Box textAlign="center">
                        <Text fontSize="2xl" fontWeight="bold" color="blue.500">
                          {dashboardData.centers.active}
                        </Text>
                        <Text fontSize="sm" color="gray.500">Centros Activos</Text>
                      </Box>
                      <Box textAlign="center">
                        <Text fontSize="2xl" fontWeight="bold">
                          {dashboardData.centers.total}
                        </Text>
                        <Text fontSize="sm" color="gray.500">Total Centros</Text>
                      </Box>
                    </SimpleGrid>
                    
                    <Box width="full">
                      <Text mb={2}>Actividad de Centros</Text>
                      <Progress 
                        value={(dashboardData.centers.active / dashboardData.centers.total) * 100} 
                        colorScheme="blue" 
                        size="md"
                      />
                      <Text fontSize="xs" color="gray.500" mt={1}>
                        {Math.round((dashboardData.centers.active / dashboardData.centers.total) * 100)}% de centros activos
                      </Text>
                    </Box>
                  </>
                )}
                
                <Button 
                  size="sm" 
                  colorScheme="orange" 
                  variant="outline"
                  onClick={() => window.location.href = '/dashboard/verificaciones/centros'}
                >
                  Gestionar Centros
                </Button>
              </VStack>
            </CardBody>
          </Card>
        </SimpleGrid>

        {/* Recent Verifications */}
        <Card bg={bgColor}>
          <CardHeader>
            <HStack justify="space-between">
              <Heading size="md">Verificaciones Recientes</Heading>
              <Button 
                size="sm" 
                colorScheme="orange" 
                variant="outline"
                onClick={() => window.location.href = '/dashboard/verificaciones'}
              >
                Ver Todas
              </Button>
            </HStack>
          </CardHeader>
          <CardBody>
            {recentVerifications && (
              <Table variant="simple" size="sm">
                <Thead>
                  <Tr>
                    <Th>Placa</Th>
                    <Th>Estado</Th>
                    <Th>Fecha</Th>
                    <Th>Centro</Th>
                    <Th>Próxima Verificación</Th>
                  </Tr>
                </Thead>
                <Tbody>
                  {recentVerifications.verifications.slice(0, 5).map((verification: any) => (
                    <Tr key={verification._id}>
                      <Td fontWeight="bold">{verification.vehiclePlate}</Td>
                      <Td>
                        <Badge colorScheme={getStatusColor(verification.status)} size="sm">
                          {getStatusText(verification.status)}
                        </Badge>
                      </Td>
                      <Td>{new Date(verification.verificationDate).toLocaleDateString()}</Td>
                      <Td fontSize="sm">{verification.verificationCenter?.name}</Td>
                      <Td fontSize="sm">{new Date(verification.nextVerificationDate).toLocaleDateString()}</Td>
                    </Tr>
                  ))}
                </Tbody>
              </Table>
            )}
          </CardBody>
        </Card>

        {/* Performance Metrics */}
        <SimpleGrid columns={[1, 3]} spacing={6}>
          <Card bg={bgColor}>
            <CardHeader>
              <Heading size="sm">Promedio por Día</Heading>
            </CardHeader>
            <CardBody>
              <Text fontSize="2xl" fontWeight="bold" color="blue.500">
                {dashboardData ? Math.round(dashboardData.recent.last30Days / 30) : 0}
              </Text>
              <Text fontSize="sm" color="gray.500">verificaciones/día</Text>
            </CardBody>
          </Card>

          <Card bg={bgColor}>
            <CardHeader>
              <Heading size="sm">Tiempo Promedio</Heading>
            </CardHeader>
            <CardBody>
              <Text fontSize="2xl" fontWeight="bold" color="purple.500">
                2.3
              </Text>
              <Text fontSize="sm" color="gray.500">días para completar</Text>
            </CardBody>
          </Card>

          <Card bg={bgColor}>
            <CardHeader>
              <Heading size="sm">Eficiencia</Heading>
            </CardHeader>
            <CardBody>
              <Text fontSize="2xl" fontWeight="bold" color="green.500">
                {dashboardData?.summary.completionRate || 0}%
              </Text>
              <Text fontSize="sm" color="gray.500">tasa de finalización</Text>
            </CardBody>
          </Card>
        </SimpleGrid>

        {dashboardLoading && (
          <Alert status="info">
            <AlertIcon />
            Cargando datos del dashboard...
          </Alert>
        )}
      </VStack>
      </Container>
    </Suspense>
  );
}