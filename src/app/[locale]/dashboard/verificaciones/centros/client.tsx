'use client';

import { useState, Suspense } from 'react';
import {
  Box,
  Container,
  Heading,
  VStack,
  Card,
  CardHeader,
  CardBody,
  Button,
  HStack,
  Input,
  InputGroup,
  InputLeftElement,
  Select,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  Badge,
  IconButton,
  useToast,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalFooter,
  ModalBody,
  ModalCloseButton,
  useDisclosure,
  FormControl,
  FormLabel,
  useColorModeValue,
  Alert,
  AlertIcon,
  Divider,
} from '@chakra-ui/react';
import { FiSearch, FiPlus, FiEye, FiEdit, FiMapPin, FiTool } from 'react-icons/fi';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { getVerificationCenters, createVerificationCenter, getCenterStats } from '@/actions/verification';
import UserDiagnostics from '@/components/verification/UserDiagnostics';

export function VerificationCentersPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [stateFilter, setStateFilter] = useState('');
  const [selectedCenter, setSelectedCenter] = useState<any>(null);
  const [showDiagnostics, setShowDiagnostics] = useState(false);

  console.log('VerificationCentersPage renderizando...');
  const [newCenter, setNewCenter] = useState({
    name: '',
    code: '',
    state: 'CDMX' as 'CDMX' | 'EDOMEX',
    organizationId: '507f1f77bcf86cd799439011', // Default organization ID
    location: {
      address: '',
      city: '',
      state: 'CDMX',
    },
    authorizedFor: ['gasoline', 'diesel'],
  });

  const bgColor = useColorModeValue('white', 'gray.800');
  const toast = useToast();
  const { isOpen, onOpen, onClose } = useDisclosure();
  const { isOpen: isCreateOpen, onOpen: onCreateOpen, onClose: onCreateClose } = useDisclosure();
  const queryClient = useQueryClient();

  // Fetch verification centers
  const { data: centers, isLoading: centersLoading } = useQuery({
    queryKey: ['verification-centers', stateFilter],
    queryFn: () => getVerificationCenters(stateFilter as 'CDMX' | 'EDOMEX' | undefined),
  });

  // Fetch center stats when a center is selected
  const { data: centerStats } = useQuery({
    queryKey: ['center-stats', selectedCenter?._id],
    queryFn: () => getCenterStats(selectedCenter._id),
    enabled: !!selectedCenter?._id,
  });

  // Create center mutation
  const createCenterMutation = useMutation({
    mutationFn: createVerificationCenter,
    onSuccess: () => {
      toast({
        title: 'Centro creado exitosamente',
        status: 'success',
        duration: 3000,
        isClosable: true,
      });
      queryClient.invalidateQueries({ queryKey: ['verification-centers'] });
      onCreateClose();
      setNewCenter({
        name: '',
        code: '',
        state: 'CDMX',
        organizationId: '507f1f77bcf86cd799439011',
        location: {
          address: '',
          city: '',
          state: 'CDMX',
        },
        authorizedFor: ['gasoline', 'diesel'],
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Error al crear centro',
        description: error.message,
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    },
  });

  const filteredCenters = centers?.filter(center =>
    center.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    center.code.toLowerCase().includes(searchTerm.toLowerCase())
  ) || [];

  const handleCreateCenter = () => {
    if (!newCenter.name || !newCenter.code) {
      toast({
        title: 'Campos requeridos',
        description: 'Por favor completa el nombre y código del centro',
        status: 'warning',
        duration: 3000,
        isClosable: true,
      });
      return;
    }

    console.log('Creando centro:', newCenter);
    createCenterMutation.mutate(newCenter);
  };

  const getStatusColor = (isActive: boolean) => {
    return isActive ? 'green' : 'red';
  };

  const getStatusText = (isActive: boolean) => {
    return isActive ? 'Activo' : 'Inactivo';
  };

  return (
    <Container maxW="7xl" py={8}>
      <VStack spacing={6} align="stretch">
        {/* Header */}
        <Card bg={bgColor}>
          <CardHeader>
            <HStack justify="space-between">
              <Heading size="lg">Gestión de Centros de Verificación</Heading>
              <HStack>
                <Button leftIcon={<FiTool />} variant="outline" colorScheme="blue" onClick={() => setShowDiagnostics(true)}>
                  Diagnóstico
                </Button>
                <Button
                  leftIcon={<FiPlus />}
                  colorScheme="orange"
                  variant="solid"
                  onClick={onCreateOpen}
                  _hover={{ bg: "orange.600" }}
                  bg="orange.500"
                  color="white"
                >
                  Nuevo Centro
                </Button>
              </HStack>
            </HStack>
          </CardHeader>
          <CardBody>
            <HStack spacing={4}>
              <InputGroup maxW="300px">
                <InputLeftElement pointerEvents="none">
                  <FiSearch color="gray.300" />
                </InputLeftElement>
                <Input
                  placeholder="Buscar centro..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </InputGroup>

              <Select maxW="200px" value={stateFilter} onChange={(e) => setStateFilter(e.target.value)}>
                <option value="">Todos los estados</option>
                <option value="CDMX">CDMX</option>
                <option value="EDOMEX">Estado de México</option>
              </Select>
            </HStack>
          </CardBody>
        </Card>

        {/* Centers Table */}
        <Card bg={bgColor}>
          <CardHeader>
            <Heading size="md">Centros Registrados ({filteredCenters.length})</Heading>
          </CardHeader>
          <CardBody>
            {centersLoading ? (
              <Alert status="info">
                <AlertIcon />
                Cargando centros de verificación...
              </Alert>
            ) : (
              <Table variant="simple">
                <Thead>
                  <Tr>
                    <Th>Código</Th>
                    <Th>Nombre</Th>
                    <Th>Estado</Th>
                    <Th>Ubicación</Th>
                    <Th>Estado</Th>
                    <Th>Acciones</Th>
                  </Tr>
                </Thead>
                <Tbody>
                  {filteredCenters.map((center: any) => (
                    <Tr key={center._id}>
                      <Td fontWeight="bold" color="orange.500">{center.code}</Td>
                      <Td>{center.name}</Td>
                      <Td>{center.state}</Td>
                      <Td>
                        <VStack align="start" spacing={0}>
                          <Box fontSize="sm">{center.location?.city}</Box>
                          <Box fontSize="xs" color="gray.500">{center.location?.address}</Box>
                        </VStack>
                      </Td>
                      <Td>
                        <Badge colorScheme={getStatusColor(center.isActive)}>
                          {getStatusText(center.isActive)}
                        </Badge>
                      </Td>
                      <Td>
                        <HStack>
                          <IconButton
                            aria-label="Ver detalles"
                            icon={<FiEye />}
                            size="sm"
                            onClick={() => {
                              setSelectedCenter(center);
                              onOpen();
                            }}
                          />
                          <IconButton
                            aria-label="Editar centro"
                            icon={<FiEdit />}
                            size="sm"
                            colorScheme="blue"
                            variant="outline"
                          />
                        </HStack>
                      </Td>
                    </Tr>
                  ))}
                </Tbody>
              </Table>
            )}

            {!centersLoading && filteredCenters.length === 0 && (
              <Alert status="info">
                <AlertIcon />
                {searchTerm || stateFilter ? 'No se encontraron centros con los filtros aplicados' : 'No hay centros registrados'}
              </Alert>
            )}
          </CardBody>
        </Card>
      </VStack>

      {/* Modal de detalles del centro */}
      <Modal isOpen={isOpen} onClose={onClose} size="xl">
        <ModalOverlay />
        <ModalContent>
          <ModalHeader>
            <HStack>
              <FiMapPin />
              <Box>Detalles del Centro</Box>
            </HStack>
          </ModalHeader>
          <ModalCloseButton />
          <ModalBody>
            {selectedCenter && (
              <VStack spacing={4} align="start">
                <Box>
                  <Box fontWeight="bold" color="orange.500">Código:</Box>
                  <Box fontSize="lg">{selectedCenter.code}</Box>
                </Box>

                <Box>
                  <Box fontWeight="bold">Nombre:</Box>
                  <Box>{selectedCenter.name}</Box>
                </Box>

                <Box>
                  <Box fontWeight="bold">Estado:</Box>
                  <Box>{selectedCenter.state}</Box>
                </Box>

                <Box>
                  <Box fontWeight="bold">Ubicación:</Box>
                  <Box>{selectedCenter.location?.address}</Box>
                  <Box color="gray.500">{selectedCenter.location?.city}, {selectedCenter.location?.state}</Box>
                </Box>

                <Box>
                  <Box fontWeight="bold">Estado:</Box>
                  <Badge colorScheme={getStatusColor(selectedCenter.isActive)}>
                    {getStatusText(selectedCenter.isActive)}
                  </Badge>
                </Box>

                {centerStats && (
                  <>
                    <Divider />
                    <Box>
                      <Box fontWeight="bold" mb={2}>Estadísticas:</Box>
                      <VStack align="start" spacing={2}>
                        <HStack>
                          <Box>Total verificaciones:</Box>
                          <Box fontWeight="bold">{centerStats.total}</Box>
                        </HStack>
                        <HStack>
                          <Box>Pendientes cliente:</Box>
                          <Box fontWeight="bold" color="yellow.500">{centerStats.pendingCustomer}</Box>
                        </HStack>
                        <HStack>
                          <Box>Completadas:</Box>
                          <Box fontWeight="bold" color="green.500">{centerStats.completed}</Box>
                        </HStack>
                        <HStack>
                          <Box>Este mes:</Box>
                          <Box fontWeight="bold">{centerStats.thisMonth}</Box>
                        </HStack>
                        <HStack>
                          <Box>Tasa de finalización:</Box>
                          <Box fontWeight="bold">{centerStats.completionRate}%</Box>
                        </HStack>
                      </VStack>
                    </Box>
                  </>
                )}
              </VStack>
            )}
          </ModalBody>
          <ModalFooter>
            <Button onClick={onClose} colorScheme="gray">Cerrar</Button>
          </ModalFooter>
        </ModalContent>
      </Modal>

      {/* Modal para crear nuevo centro */}
      <Modal isOpen={isCreateOpen} onClose={onCreateClose} size="lg">
        <ModalOverlay />
        <ModalContent>
          <ModalHeader>Crear Nuevo Centro de Verificación</ModalHeader>
          <ModalCloseButton />
          <ModalBody>
            <VStack spacing={4}>
              <FormControl isRequired>
                <FormLabel>Nombre del Centro</FormLabel>
                <Input
                  value={newCenter.name}
                  onChange={(e) => setNewCenter({ ...newCenter, name: e.target.value })}
                  placeholder="Verificentro Central"
                />
              </FormControl>

              <FormControl isRequired>
                <FormLabel>Código del Centro</FormLabel>
                <Input
                  value={newCenter.code}
                  onChange={(e) => setNewCenter({ ...newCenter, code: e.target.value.toUpperCase() })}
                  placeholder="MH01"
                />
              </FormControl>

              <FormControl isRequired>
                <FormLabel>Estado</FormLabel>
                <Select
                  value={newCenter.state}
                  onChange={(e) => setNewCenter({
                    ...newCenter,
                    state: e.target.value as 'CDMX' | 'EDOMEX',
                    location: { ...newCenter.location, state: e.target.value }
                  })}
                  placeholder="Selecciona un estado"
                >
                  <option value="CDMX">Ciudad de México</option>
                  <option value="EDOMEX">Estado de México</option>
                </Select>
              </FormControl>

              <FormControl>
                <FormLabel>ID de Organización</FormLabel>
                <Input
                  value={newCenter.organizationId}
                  isReadOnly
                  bg="gray.100"
                  placeholder="ID de la organización propietaria"
                />
              </FormControl>

              <FormControl>
                <FormLabel>Dirección</FormLabel>
                <Input
                  value={newCenter.location.address}
                  onChange={(e) => setNewCenter({
                    ...newCenter,
                    location: { ...newCenter.location, address: e.target.value }
                  })}
                  placeholder="Av. Insurgentes Sur 123"
                />
              </FormControl>

              <FormControl>
                <FormLabel>Ciudad</FormLabel>
                <Input
                  value={newCenter.location.city}
                  onChange={(e) => setNewCenter({
                    ...newCenter,
                    location: { ...newCenter.location, city: e.target.value }
                  })}
                  placeholder={newCenter.state === 'CDMX' ? 'Ciudad de México' : 'Nezahualcóyotl'}
                />
              </FormControl>
            </VStack>
          </ModalBody>
          <ModalFooter>
            <Button variant="ghost" mr={3} onClick={onCreateClose} colorScheme="gray">
              Cancelar
            </Button>
            <Button
              variant="outline"
              colorScheme="blue"
              mr={3}
              onClick={() => {
                setNewCenter({
                  name: 'Verificentro Ejemplo',
                  code: 'EJ01',
                  state: 'CDMX',
                  organizationId: '507f1f77bcf86cd799439011',
                  location: {
                    address: 'Av. Insurgentes Sur 123',
                    city: 'Ciudad de México',
                    state: 'CDMX',
                  },
                  authorizedFor: ['gasoline', 'diesel'],
                });
              }}
            >
              Datos de Ejemplo
            </Button>
            <Button
              colorScheme="blue"
              onClick={handleCreateCenter}
              isLoading={createCenterMutation.isPending}
            >
              Crear Centro
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>

      {/* Diagnóstico de Usuario */}
      {showDiagnostics && (
        <UserDiagnostics onClose={() => setShowDiagnostics(false)} />
      )}
    </Container>
  );
}