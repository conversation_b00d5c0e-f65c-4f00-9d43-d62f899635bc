'use client';

import { useState, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import {
  Box,
  Button,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  Badge,
  useDisclosure,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalFooter,
  ModalCloseButton,
  FormControl,
  FormLabel,
  Input,
  Select,
  VStack,
  HStack,
  useToast,
  Text,
  Spinner,
  Alert,
  AlertIcon,
} from '@chakra-ui/react';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import { VerificationCenter } from '@/types';
import { getVerificationCenters, createVerificationCenter } from '@/actions/verification';

const validationSchema = Yup.object({
  name: Yup.string().required('Nombre es requerido'),
  code: Yup.string().required('Código es requerido'),
  street: Yup.string().required('Calle es requerida'),
  number: Yup.string().required('Número es requerido'),
  colony: Yup.string().required('Colonia es requerida'),
  city: Yup.string().required('Ciudad es requerida'),
  postalCode: Yup.string().required('Código postal es requerido'),
  state: Yup.string().required('Estado es requerido'),
});

interface VerificationCenterManagerProps {
  allowCreate?: boolean;
}

export function VerificationCenterManager({ allowCreate = false }: VerificationCenterManagerProps) {
  const [filterState, setFilterState] = useState<'CDMX' | 'EDOMEX' | ''>('');
  const { isOpen, onOpen, onClose } = useDisclosure();
  const toast = useToast();
  const queryClient = useQueryClient();

  const { data: centers, isLoading, error } = useQuery({
    queryKey: ['verification-centers', filterState],
    queryFn: () => getVerificationCenters(filterState || undefined),
  });

  const createMutation = useMutation({
    mutationFn: createVerificationCenter,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['verification-centers'] });
      toast({
        title: 'Verificentro creado',
        description: 'El verificentro se ha creado exitosamente',
        status: 'success',
        duration: 5000,
        isClosable: true,
      });
      onClose();
      formik.resetForm();
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.message || 'Error al crear el verificentro',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    },
  });

  const formik = useFormik({
    initialValues: {
      name: '',
      code: '',
      street: '',
      number: '',
      colony: '',
      city: '',
      postalCode: '',
      state: '' as 'CDMX' | 'EDOMEX',
    },
    validationSchema,
    onSubmit: (values) => {
      createMutation.mutate({
        name: values.name,
        code: values.code,
        organizationId: '507f1f77bcf86cd799439011', // Default organization ID - should be dynamic
        location: {
          address: `${values.street} ${values.number}, ${values.colony}`,
          city: values.city,
          state: values.state,
        },
        authorizedFor: ['gasoline', 'diesel'],
        state: values.state,
      });
    },
  });

  const getStatusColor = (isActive: boolean) => {
    return isActive ? 'green' : 'red';
  };

  const getStatusText = (isActive: boolean) => {
    return isActive ? 'Activo' : 'Inactivo';
  };

  if (isLoading) {
    return (
      <Box display="flex" justifyContent="center" p={8}>
        <Spinner size="lg" />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert status="error">
        <AlertIcon />
        Error al cargar los verificentros
      </Alert>
    );
  }

  return (
    <Box>
      <HStack mb={4} justifyContent="space-between">
        <HStack>
          <Text fontSize="lg" fontWeight="bold">
            Verificentros
          </Text>
          <Select 
            value={filterState} 
            onChange={(e) => setFilterState(e.target.value as 'CDMX' | 'EDOMEX' | '')}
            width="200px"
          >
            <option value="">Todos los estados</option>
            <option value="CDMX">CDMX</option>
            <option value="EDOMEX">Estado de México</option>
          </Select>
        </HStack>
        {allowCreate && (
          <Button colorScheme="blue" onClick={onOpen}>
            Nuevo Verificentro
          </Button>
        )}
      </HStack>

      <Table variant="simple">
        <Thead>
          <Tr>
            <Th>Código</Th>
            <Th>Nombre</Th>
            <Th>Estado</Th>
            <Th>Ciudad</Th>
            <Th>Dirección</Th>
            <Th>Estado</Th>
          </Tr>
        </Thead>
        <Tbody>
          {centers?.map((center) => (
            <Tr key={center._id}>
              <Td fontWeight="bold">{center.code}</Td>
              <Td>{center.name}</Td>
              <Td>{center.state}</Td>
              <Td>{center.location?.city}</Td>
              <Td>
                {center.location?.address}
              </Td>
              <Td>
                <Badge colorScheme="green">
                  Activo
                </Badge>
              </Td>
            </Tr>
          ))}
        </Tbody>
      </Table>

      {centers?.length === 0 && (
        <Box textAlign="center" py={8}>
          <Text color="gray.500">No hay verificentros disponibles</Text>
        </Box>
      )}

      <Modal isOpen={isOpen} onClose={onClose} size="lg">
        <ModalOverlay />
        <ModalContent>
          <ModalHeader>Crear Nuevo Verificentro</ModalHeader>
          <ModalCloseButton />
          <form onSubmit={formik.handleSubmit}>
            <ModalBody>
              <VStack spacing={4}>
                <FormControl isRequired isInvalid={!!formik.errors.name && formik.touched.name}>
                  <FormLabel>Nombre</FormLabel>
                  <Input
                    name="name"
                    value={formik.values.name}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    placeholder="Verificentro MH01"
                  />
                  {formik.errors.name && formik.touched.name && (
                    <Text color="red.500" fontSize="sm">{formik.errors.name}</Text>
                  )}
                </FormControl>

                <FormControl isRequired isInvalid={!!formik.errors.code && formik.touched.code}>
                  <FormLabel>Código</FormLabel>
                  <Input
                    name="code"
                    value={formik.values.code}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    placeholder="MH01"
                  />
                  {formik.errors.code && formik.touched.code && (
                    <Text color="red.500" fontSize="sm">{formik.errors.code}</Text>
                  )}
                </FormControl>

                <FormControl isRequired isInvalid={!!formik.errors.state && formik.touched.state}>
                  <FormLabel>Estado</FormLabel>
                  <Select
                    name="state"
                    value={formik.values.state}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    placeholder="Seleccionar estado"
                  >
                    <option value="CDMX">CDMX</option>
                    <option value="EDOMEX">Estado de México</option>
                  </Select>
                  {formik.errors.state && formik.touched.state && (
                    <Text color="red.500" fontSize="sm">{formik.errors.state}</Text>
                  )}
                </FormControl>

                <HStack width="100%">
                  <FormControl isRequired isInvalid={!!formik.errors.street && formik.touched.street}>
                    <FormLabel>Calle</FormLabel>
                    <Input
                      name="street"
                      value={formik.values.street}
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                      placeholder="Av. Insurgentes Sur"
                    />
                    {formik.errors.street && formik.touched.street && (
                      <Text color="red.500" fontSize="sm">{formik.errors.street}</Text>
                    )}
                  </FormControl>

                  <FormControl isRequired isInvalid={!!formik.errors.number && formik.touched.number}>
                    <FormLabel>Número</FormLabel>
                    <Input
                      name="number"
                      value={formik.values.number}
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                      placeholder="1234"
                    />
                    {formik.errors.number && formik.touched.number && (
                      <Text color="red.500" fontSize="sm">{formik.errors.number}</Text>
                    )}
                  </FormControl>
                </HStack>

                <HStack width="100%">
                  <FormControl isRequired isInvalid={!!formik.errors.colony && formik.touched.colony}>
                    <FormLabel>Colonia</FormLabel>
                    <Input
                      name="colony"
                      value={formik.values.colony}
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                      placeholder="Del Valle"
                    />
                    {formik.errors.colony && formik.touched.colony && (
                      <Text color="red.500" fontSize="sm">{formik.errors.colony}</Text>
                    )}
                  </FormControl>

                  <FormControl isRequired isInvalid={!!formik.errors.city && formik.touched.city}>
                    <FormLabel>Ciudad</FormLabel>
                    <Input
                      name="city"
                      value={formik.values.city}
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                      placeholder="Ciudad de México"
                    />
                    {formik.errors.city && formik.touched.city && (
                      <Text color="red.500" fontSize="sm">{formik.errors.city}</Text>
                    )}
                  </FormControl>
                </HStack>

                <FormControl isRequired isInvalid={!!formik.errors.postalCode && formik.touched.postalCode}>
                  <FormLabel>Código Postal</FormLabel>
                  <Input
                    name="postalCode"
                    value={formik.values.postalCode}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    placeholder="03100"
                  />
                  {formik.errors.postalCode && formik.touched.postalCode && (
                    <Text color="red.500" fontSize="sm">{formik.errors.postalCode}</Text>
                  )}
                </FormControl>
              </VStack>
            </ModalBody>

            <ModalFooter>
              <Button variant="ghost" mr={3} onClick={onClose}>
                Cancelar
              </Button>
              <Button 
                colorScheme="blue" 
                type="submit"
                isLoading={createMutation.isPending}
              >
                Crear Verificentro
              </Button>
            </ModalFooter>
          </form>
        </ModalContent>
      </Modal>
    </Box>
  );
}